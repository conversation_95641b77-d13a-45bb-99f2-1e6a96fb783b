package com.yancao.qrscanner.domain

import androidx.xr.runtime.math.BoundingBox
import com.google.mlkit.vision.barcode.common.Barcode
import java.text.SimpleDateFormat
import java.util.*

/**
 * 扫描记录数据类
 * 包含二维码值、位置信息和时间戳
 */
data class ScanRecord(
    val qrCodeValue: String,
    val boundingBox: Barcode.BoundingBox?,
    val cornerPoints: Array<android.graphics.Point>?,
    val timestamp: Long = System.currentTimeMillis(),
    val imageWidth: Int,
    val imageHeight: Int
) {
    // 格式化时间显示
    val formattedTime: String
        get() = SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS", Locale.getDefault()).format(Date(timestamp))

    // 位置信息字符串
    val positionInfo: String
        get() {
            return boundingBox?.let { box ->
                "Left:${box.left}, Top:${box.top}, Right:${box.right}, Bottom:${box.bottom}"
            } ?: "位置信息不可用"
        }

    // 角点信息字符串
    val cornerPointsInfo: String
        get() {
            return cornerPoints?.let { points ->
                points.joinToString("; ") { "(${ it.x}, ${it.y})" }
            } ?: "角点信息不可用"
        }

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as ScanRecord

        if (qrCodeValue != other.qrCodeValue) return false
        if (boundingBox != other.boundingBox) return false
        if (cornerPoints != null) {
            if (other.cornerPoints == null) return false
            if (!cornerPoints.contentEquals(other.cornerPoints)) return false
        } else if (other.cornerPoints != null) return false

        return true
    }

    override fun hashCode(): Int {
        var result = qrCodeValue.hashCode()
        result = 31 * result + (boundingBox?.hashCode() ?: 0)
        result = 31 * result + (cornerPoints?.contentHashCode() ?: 0)
        return result
    }
}